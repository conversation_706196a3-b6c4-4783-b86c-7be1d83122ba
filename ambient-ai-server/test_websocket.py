#!/usr/bin/env python3
"""
Test script for the WebSocket functionality of the Ambient AI Server.

This script creates sample transcript chunks and sends them to the server
to verify the complete data flow.
"""

import asyncio
import time
import websockets
from src.ambient_ai_server.transcript_utils import Transcript<PERSON><PERSON>ler


async def test_websocket_connection():
    """Test the WebSocket connection and data flow."""
    
    print("🔄 Testing WebSocket connection...")
    
    # Initialize transcript handler
    handler = TranscriptHandler()
    
    try:
        # Connect to the server
        uri = "ws://localhost:8000/ws"
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to server")
            
            # Receive welcome message
            welcome_msg = await websocket.recv()
            print(f"📨 Server: {welcome_msg}")
            
            # Create and send test chunks
            now = int(time.time() * 1000)
            
            # Test 1: Audio chunk
            print("\n📤 Sending audio chunk...")
            audio_chunk = handler.create_audio_chunk(
                pcm_data=b"fake_audio_data_for_testing",
                client_start_time=now - 200,
                client_end_time=now
            )
            await websocket.send(audio_chunk)
            print("✅ Audio chunk sent")
            
            # Test 2: Video chunk
            print("📤 Sending video chunk...")
            video_chunk = handler.create_video_chunk(
                jpeg_data=b"fake_jpeg_data_for_testing",
                camera_type="front",
                client_start_time=now,
                client_end_time=now + 33  # ~30fps
            )
            await websocket.send(video_chunk)
            print("✅ Video chunk sent")
            
            # Test 3: IMU chunk
            print("📤 Sending IMU chunk...")
            imu_chunk = handler.create_imu_chunk(
                accel_x=0.1, accel_y=0.2, accel_z=9.8,
                gyro_x=0.01, gyro_y=0.02, gyro_z=0.03,
                gravity_x=0.0, gravity_y=0.0, gravity_z=9.8,
                client_start_time=now + 50,
                client_end_time=now + 100
            )
            await websocket.send(imu_chunk)
            print("✅ IMU chunk sent")
            
            # Test 4: Location chunk
            print("📤 Sending location chunk...")
            location_chunk = handler.create_location_chunk(
                latitude=37.7749,
                longitude=-122.4194,
                altitude=100.0,
                speed=5.0,
                accuracy=10.0,
                gps_timestamp=now,
                client_start_time=now + 100,
                client_end_time=now + 150
            )
            await websocket.send(location_chunk)
            print("✅ Location chunk sent")
            
            # Test 5: Text chunk
            print("📤 Sending text chunk...")
            text_chunk = handler.create_text_chunk(
                message="Test message from WebSocket client",
                client_start_time=now + 150,
                client_end_time=now + 200
            )
            await websocket.send(text_chunk)
            print("✅ Text chunk sent")
            
            # Wait a moment for processing
            await asyncio.sleep(1)
            
            print("\n🎉 All test chunks sent successfully!")
            
    except Exception as e:
        print(f"❌ Error during WebSocket test: {e}")
        raise


async def test_analytics():
    """Test the analytics endpoint after sending data."""
    
    print("\n📊 Checking analytics...")
    
    import aiohttp
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8000/analytics') as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ Total chunks received: {data['chunks']['total_received']}")
                    print(f"✅ Modality breakdown: {data['chunks']['by_modality']}")
                    print(f"✅ Server uptime: {data['server_info']['uptime_human']}")
                else:
                    print(f"❌ Analytics request failed: {response.status}")
    except Exception as e:
        print(f"❌ Error checking analytics: {e}")


async def main():
    """Main test function."""
    
    print("🧪 Starting Ambient AI Server WebSocket Tests")
    print("=" * 50)
    
    try:
        # Test WebSocket functionality
        await test_websocket_connection()
        
        # Test analytics
        await test_analytics()
        
        print("\n" + "=" * 50)
        print("🎉 All tests completed successfully!")
        print("💡 Check the server logs to see the received chunks")
        print("📊 Visit http://localhost:8000/analytics for real-time stats")
        
    except Exception as e:
        print(f"\n❌ Tests failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    # Install aiohttp if needed
    try:
        import aiohttp
    except ImportError:
        print("Installing aiohttp for HTTP testing...")
        import subprocess
        subprocess.run(["pip", "install", "aiohttp"], check=True)
        import aiohttp
    
    exit(asyncio.run(main()))
