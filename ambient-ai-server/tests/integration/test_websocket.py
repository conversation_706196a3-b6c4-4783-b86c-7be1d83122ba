#!/usr/bin/env python3
"""
Integration test for WebSocket functionality.
"""

import asyncio
import time
import websockets
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

from ambient_ai_server.services.transcript import Transcript<PERSON>andler


async def test_websocket_connection():
    """Test the WebSocket connection and data flow."""

    print("🔄 Testing WebSocket connection...")

    # Initialize transcript handler
    handler = TranscriptHandler()

    try:
        # Connect to the server
        uri = "ws://localhost:8000/ws"
        async with websockets.connect(uri) as websocket:
            print("✅ Connected to server")

            # Receive welcome message
            welcome_msg = await websocket.recv()
            print(f"📨 Server: {welcome_msg}")

            # Create and send test chunks
            now = int(time.time() * 1000)

            # Test: Audio chunk
            print("\n📤 Sending audio chunk...")
            audio_chunk = handler.create_audio_chunk(
                pcm_data=b"fake_audio_data_for_testing",
                client_start_time=now - 200,
                client_end_time=now
            )
            await websocket.send(audio_chunk)
            print("✅ Audio chunk sent")

            print("\n🎉 Integration test completed successfully!")

    except Exception as e:
        print(f"❌ Error during WebSocket test: {e}")
        raise


async def main():
    """Main test function."""

    print("🧪 Starting Ambient AI Server WebSocket Integration Test")
    print("=" * 50)

    try:
        await test_websocket_connection()
        return 0

    except Exception as e:
        print(f"\n❌ Integration test failed: {e}")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))